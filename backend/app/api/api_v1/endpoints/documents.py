from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, UploadFile, File, Form
from typing import List, Optional
from app.schemas.document import Document, DocumentCreate, DocumentUpdate, SignatureData, FileInfo
from app.core.auth import get_current_user
from app.db.database import get_supabase_client
from app.services.storage import StorageService
from app.utils.response import (
    handle_supabase_response,
    create_success_response,
    create_created_response,
    not_found_error,
    forbidden_error
)
from datetime import datetime
import uuid
import json

router = APIRouter()

@router.get("/documents", response_model=List[Document])
async def get_documents(
    workspace_id: str = Query(..., description="Workspace ID (required for security)"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve documents for a specific workspace that the user has access to.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Always filter by workspace_id for security
    query = supabase.table("documents").select("*").eq("workspace_id", workspace_id)

    response = query.execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to retrieve documents")

    return data

@router.post("/documents", response_model=Document)
async def create_document(
    document: DocumentCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new document.
    """
    supabase = get_supabase_client()

    # Prepare the document data
    document_data = document.model_dump()
    document_data["id"] = f"doc-{str(uuid.uuid4())}"
    document_data["created_at"] = datetime.utcnow().isoformat()
    document_data["created_by"] = current_user["id"]
    document_data["status"] = "draft"

    # Insert the document
    response = supabase.table("documents").insert(document_data).execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to create document")

    if not data:
        raise not_found_error("Document")

    return data[0]

@router.post("/documents/upload", response_model=Document)
async def upload_document_with_file(
    file: UploadFile = File(...),
    title: str = Form(...),
    workspace_id: str = Form(...),
    folder: Optional[str] = Form(""),
    content: Optional[str] = Form(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Upload a document file and create a document record.
    """
    supabase = get_supabase_client()

    try:
        # Upload file to storage
        file_result = await StorageService.upload_file(
            file=file,
            folder=folder,
            workspace_id=workspace_id,
            metadata={
                "uploaded_by": current_user["id"],
                "title": title
            }
        )

        # Create file info
        file_info = {
            "filename": file_result["filename"],
            "content_type": file_result["content_type"],
            "size": file_result["size"],
            "path": file_result["path"],
            "url": file_result["url"],
            "uploaded_at": file_result["uploaded_at"]
        }

        # Prepare document data
        document_data = {
            "id": f"doc-{str(uuid.uuid4())}",
            "title": title,
            "filename": file.filename,
            "workspace_id": workspace_id,
            "content": content,
            "file_url": file_result["url"],
            "file_path": file_result["path"],
            "file_info": file_info,
            "folder": folder,
            "created_at": datetime.utcnow().isoformat(),
            "created_by": current_user["id"],
            "status": "draft"
        }

        # Insert document
        response = supabase.table("documents").insert(document_data).execute()

        # Handle response using helper function
        try:
            data = handle_supabase_response(response, "Failed to create document")
            if not data:
                raise not_found_error("Document")
            return data[0]
        except HTTPException:
            # If document creation fails, try to delete the uploaded file
            StorageService.delete_file(file_result["path"])
            raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

@router.get("/documents/{document_id}", response_model=Document)
async def get_document(
    document_id: str = Path(..., description="The ID of the document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific document.
    """
    supabase = get_supabase_client()

    # Get the document
    response = supabase.table("documents").select("*").eq("id", document_id).execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to retrieve document")

    if not data:
        raise not_found_error("Document")

    # Get the signers for this document
    signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).order("order").execute()

    # Handle signers response
    signers_data = handle_supabase_response(signers_response, "Failed to retrieve document signers")

    # Add signers to the document
    document = data[0]
    document["signers"] = signers_data

    return document

@router.put("/documents/{document_id}", response_model=Document)
async def update_document(
    document_update: DocumentUpdate,
    document_id: str = Path(..., description="The ID of the document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific document.
    """
    supabase = get_supabase_client()

    # Check if the document exists
    check_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    # Handle check response
    check_data = handle_supabase_response(check_response, "Failed to check document existence")

    if not check_data:
        raise not_found_error("Document")

    # Prepare the update data
    update_data = document_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()

    # Update the document
    response = supabase.table("documents").update(update_data).eq("id", document_id).execute()

    # Handle update response
    updated_data = handle_supabase_response(response, "Failed to update document")

    if not updated_data:
        raise not_found_error("Document")

    return updated_data[0]

@router.post("/documents/{document_id}/sign", response_model=Document)
async def sign_document(
    signature_data: SignatureData,
    document_id: str = Path(..., description="The ID of the document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Sign a document.
    """
    supabase = get_supabase_client()

    # Check if the document exists
    document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    # Handle document response
    document_data = handle_supabase_response(document_response, "Failed to retrieve document")

    if not document_data:
        raise not_found_error("Document")

    # Check if the signer exists
    signer_response = supabase.table("document_signers").select("*").eq("id", signature_data.signer_id).execute()

    # Handle signer response
    signer_data = handle_supabase_response(signer_response, "Failed to retrieve signer")

    if not signer_data:
        raise not_found_error("Signer")

    # Update the signer status
    signer_update = {
        "status": "completed",
        "completed_at": datetime.utcnow().isoformat(),
        "signature_data": signature_data.signature.model_dump()
    }

    signer_update_response = supabase.table("document_signers").update(signer_update).eq("id", signature_data.signer_id).execute()

    # Handle signer update response
    handle_supabase_response(signer_update_response, "Failed to update signer")

    # Check if all signers have completed
    all_signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).execute()

    # Handle all signers response
    all_signers_data = handle_supabase_response(all_signers_response, "Failed to retrieve all signers")

    all_completed = all(signer["status"] == "completed" for signer in all_signers_data)

    # Update document status if all signers have completed
    if all_completed:
        document_update_response = supabase.table("documents").update({"status": "completed"}).eq("id", document_id).execute()

        # Handle document update response
        handle_supabase_response(document_update_response, "Failed to update document status")

    # Get the updated document with signers
    updated_document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    # Handle updated document response
    updated_document_data = handle_supabase_response(updated_document_response, "Failed to retrieve updated document")

    updated_signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).order("order").execute()

    # Handle updated signers response
    updated_signers_data = handle_supabase_response(updated_signers_response, "Failed to retrieve updated signers")

    # Add signers to the document
    if updated_document_data:
        updated_document = updated_document_data[0]
        updated_document["signers"] = updated_signers_data
        return updated_document

    raise not_found_error("Document")

@router.post("/documents/{document_id}/decline", response_model=Document)
async def decline_document(
    document_id: str = Path(..., description="The ID of the document"),
    decline_data: dict = Body(..., description="Decline data"),
    current_user: dict = Depends(get_current_user)
):
    """
    Decline to sign a document.
    """
    supabase = get_supabase_client()

    # Check if the document exists
    document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if document_response.error:
        raise HTTPException(status_code=400, detail=document_response.error.message)

    if not document_response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if the signer exists
    signer_response = supabase.table("document_signers").select("*").eq("id", decline_data["signer_id"]).execute()

    if signer_response.error:
        raise HTTPException(status_code=400, detail=signer_response.error.message)

    if not signer_response.data:
        raise HTTPException(status_code=404, detail="Signer not found")

    # Update the signer status
    signer_update = {
        "status": "declined",
        "decline_reason": decline_data["reason"],
        "declined_at": datetime.utcnow().isoformat()
    }

    signer_update_response = supabase.table("document_signers").update(signer_update).eq("id", decline_data["signer_id"]).execute()

    if signer_update_response.error:
        raise HTTPException(status_code=400, detail=signer_update_response.error.message)

    # Update document status
    document_update_response = supabase.table("documents").update({"status": "declined"}).eq("id", document_id).execute()

    if document_update_response.error:
        raise HTTPException(status_code=400, detail=document_update_response.error.message)

    # Get the updated document with signers
    updated_document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if updated_document_response.error:
        raise HTTPException(status_code=400, detail=updated_document_response.error.message)

    updated_signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).order("order").execute()

    if updated_signers_response.error:
        raise HTTPException(status_code=400, detail=updated_signers_response.error.message)

    # Add signers to the document
    updated_document = updated_document_response.data[0]
    updated_document["signers"] = updated_signers_response.data

    return updated_document

@router.get("/documents/{document_id}/file")
async def get_document_file_url(
    document_id: str = Path(..., description="The ID of the document"),
    expires_in: Optional[int] = Query(3600, description="Number of seconds until the URL expires"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a signed URL for a document file that expires after a certain time.
    """
    supabase = get_supabase_client()

    # Get the document
    response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    if not response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    document = response.data[0]

    # Check if document has a file path
    if not document.get("file_path"):
        raise HTTPException(status_code=404, detail="Document has no associated file")

    try:
        # Get signed URL
        signed_url = StorageService.get_file_url(
            file_path=document["file_path"],
            expires_in=expires_in
        )

        return {
            "url": signed_url,
            "expires_in": expires_in,
            "filename": document["filename"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting file URL: {str(e)}")
