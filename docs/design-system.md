# LegalAI Contract Management System Design System

## Overview

This document outlines the design system used in the LegalAI Contract Management System, providing a comprehensive reference for colors, typography, components, themes, and dark mode implementation. Following these guidelines ensures a consistent user experience and visual identity across the application.

**Last Updated**: December 2024
**Version**: 2.0 - Updated for Document Engine and Landing Page implementations

## Color Palette

### Base Colors

Our design system uses a set of HSL (Hue, Saturation, Lightness) color variables that power both light and dark themes:

| Variable | Light Mode | Dark Mode | Usage |
|----------|------------|-----------|-------|
| `--background` | 0 0% 100% | 222 18% 11% | Main background color |
| `--foreground` | 222.2 84% 4.9% | 210 40% 98% | Main text color |
| `--card` | 0 0% 100% | 222 16% 13% | Card background |
| `--card-foreground` | 222.2 84% 4.9% | 210 40% 98% | Card text color |
| `--popover` | 0 0% 100% | 222 16% 13% | Popover background |
| `--popover-foreground` | 222.2 84% 4.9% | 210 40% 98% | Popover text color |
| `--primary` | 222.2 47.4% 11.2% | 220 13% 91% | Primary actions/buttons |
| `--primary-foreground` | 210 40% 98% | 222 18% 11% | Primary button text |
| `--secondary` | 210 40% 96.1% | 222 14% 18% | Secondary elements |
| `--secondary-foreground` | 222.2 47.4% 11.2% | 210 40% 98% | Secondary text |
| `--muted` | 210 40% 96.1% | 223 14% 18% | Muted backgrounds |
| `--muted-foreground` | 215.4 16.3% 46.9% | 215 20.2% 75% | Muted text |
| `--accent` | 210 40% 96.1% | 220 13% 91% | Accent highlights |
| `--accent-foreground` | 222.2 47.4% 11.2% | 222 18% 11% | Accent text |
| `--destructive` | 0 84.2% 60.2% | 0 62.8% 30.6% | Error/delete actions |
| `--destructive-foreground` | 210 40% 98% | 210 40% 98% | Error text |
| `--border` | 214.3 31.8% 91.4% | 216 15% 25% | Borders |
| `--input` | 214.3 31.8% 91.4% | 216 15% 25% | Input fields |
| `--ring` | 222.2 84% 4.9% | 220 13% 91% | Focus rings |

### Semantic Tag Colors

We use a defined set of colors for tagging and categorization:

| Color Name | Hex Value | Usage |
|------------|-----------|-------|
| Red | #ef4444 | Warning, errors, urgent items |
| Green | #22c55e | Success, approved, completed |
| Blue | #3b82f6 | Information, payment, default |
| Yellow | #eab308 | Pending, awaiting action |
| Purple | #a855f7 | Duration, specialized items |
| Pink | #ec4899 | Special categories |
| Orange | #f97316 | Warnings, awaiting review |
| Gray | #6b7280 | Neutral, disabled |

#### Semantic Categories
| Category | Color | Usage |
|----------|-------|-------|
| Payment | Blue (#3b82f6) | Payment terms, financial details |
| Scope | Green (#22c55e) | Project scope, deliverables |
| Duration | Purple (#a855f7) | Time periods, deadlines |
| Deliverable | Amber (#f59e0b) | Product/service deliverables |

### Tag Color Variants

Each tag color has three style variants:

```
TAG_COLOR_VARIANTS: {
  red: { bg: "#fef2f2", text: "#b91c1c", border: "#fee2e2" },
  green: { bg: "#f0fdf4", text: "#166534", border: "#dcfce7" },
  blue: { bg: "#eff6ff", text: "#1e40af", border: "#dbeafe" },
  yellow: { bg: "#fefce8", text: "#854d0e", border: "#fef9c3" },
  purple: { bg: "#faf5ff", text: "#7e22ce", border: "#f3e8ff" },
  pink: { bg: "#fdf2f8", text: "#be185d", border: "#fce7f3" },
  orange: { bg: "#fff7ed", text: "#c2410c", border: "#ffedd5" },
  gray: { bg: "#f9fafb", text: "#374151", border: "#f3f4f6" },
}
```

## Typography

### Font Family

The application uses the Inter font family as the primary typeface:

```css
font-family: 'Inter', sans-serif;
```

### Font Sizes

Font sizes are managed through Tailwind CSS classes:

| Class | Size | Usage |
|-------|------|-------|
| text-xs | 0.75rem | Secondary information, metadata, labels |
| text-sm | 0.875rem | Card titles, form labels, compact UI elements |
| text-base | 1rem | Default body text, page subtitles |
| text-lg | 1.125rem | Reserved for special emphasis |
| text-xl | 1.25rem | Reserved for special emphasis |
| text-2xl | 1.5rem | Page titles, main headings |
| text-3xl | 1.875rem | Reserved for hero sections |
| text-4xl | 2.25rem | Reserved for hero sections |
| text-5xl | 3rem | Reserved for hero sections |

#### Font Size Usage Guidelines

- Use `text-xs` for metadata, secondary information, button text in compact UIs
- Use `text-sm` for card titles, form labels, and most UI components
- Use `text-base` for regular body text and page subtitles
- Use `text-2xl` for page titles
- Larger font sizes (text-3xl and above) should be used sparingly and only for hero sections or special emphasis

### Line Heights

Line heights are controlled via Tailwind CSS:

| Class | Line Height | Usage |
|-------|-------------|-------|
| leading-none | 1 | Very tight, headlines |
| leading-tight | 1.25 | Tight, headings |
| leading-snug | 1.375 | Slightly tighter than normal |
| leading-normal | 1.5 | Default for body text |
| leading-relaxed | 1.625 | Slightly looser than normal |
| leading-loose | 2 | Very loose, for readability |

## Spacing and Layout

### Border Radius

Border radius values are defined as CSS variables:

| Variable | Value | Usage |
|----------|-------|-------|
| `--radius` | 0.5rem | Default radius |
| `--radius-md` | calc(var(--radius) - 2px) | Medium radius |
| `--radius-sm` | calc(var(--radius) - 4px) | Small radius |

### Spacing

Spacing follows the Tailwind CSS scale but includes additional utilities for mobile safe areas:

| Utility | Value | Usage |
|---------|-------|-------|
| safe-top | env(safe-area-inset-top) | Safe area padding for top |
| safe-bottom | env(safe-area-inset-bottom) | Safe area padding for bottom |
| safe-left | env(safe-area-inset-left) | Safe area padding for left |
| safe-right | env(safe-area-inset-right) | Safe area padding for right |

#### Page Layout Spacing

| Class | Value | Usage |
|-------|-------|-------|
| p-4 | 1rem | Standard page padding |
| gap-2 | 0.5rem | Tight spacing between related elements |
| gap-3 | 0.75rem | Standard spacing between related elements |
| gap-4 | 1rem | Spacing between distinct sections |
| mb-2 | 0.5rem | Small vertical spacing |
| mb-4 | 1rem | Standard vertical spacing between sections |
| mt-2 | 0.5rem | Small top margin |
| mt-4 | 1rem | Standard top margin |

#### Component Spacing

| Class | Value | Usage |
|-------|-------|-------|
| p-2 | 0.5rem | Compact component padding |
| p-3 | 0.75rem | Standard component padding |
| px-2 | 0.5rem (horizontal) | Compact horizontal padding |
| py-1 | 0.25rem (vertical) | Compact vertical padding |
| space-y-2 | 0.5rem (gap) | Vertical spacing between stacked elements |
| space-x-2 | 0.5rem (gap) | Horizontal spacing between inline elements |

## Theme System

### Theme Types

The application supports three theme modes:

| Theme | Description |
|-------|-------------|
| `light` | Light mode with white backgrounds and dark text |
| `dark` | Dark mode with dark backgrounds and light text |
| `system` | Follows the user's system preference |

### Theme Implementation

The theme system is implemented using React context and local storage:

```tsx
// Theme provider that manages state and persistence
export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "ui-theme",
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    // Initialize from localStorage or fallback to default
    if (typeof window === "undefined") return defaultTheme;
    const savedTheme = localStorage.getItem(storageKey) as Theme | null;
    if (savedTheme && ["dark", "light", "system"].includes(savedTheme)) {
      return savedTheme;
    }
    return defaultTheme;
  });

  // Apply theme to document and handle system changes
  useEffect(() => {
    if (typeof window === "undefined") return;

    const root = window.document.documentElement;
    root.classList.remove("light", "dark");

    const effectiveTheme = theme === "system"
      ? (window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light")
      : theme;

    root.classList.add(effectiveTheme);

    // Listen for system theme changes
    if (theme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = () => {
        applyThemeToDOM("system");
      };
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }
  }, [theme]);
}
```

### Theme Toggle Component

```tsx
const ThemeToggle = ({
  variant = "outline",
  size = "icon",
  className = "",
}: ThemeToggleProps) => {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleTheme}
      className={className}
    >
      {theme === "dark" ? (
        <Sun className="h-[1.2rem] w-[1.2rem]" />
      ) : (
        <Moon className="h-[1.2rem] w-[1.2rem]" />
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
};
```

## Components

The design system includes a collection of reusable UI components built with accessibility in mind. Here are some key components:

### Buttons

Buttons come in several variants:

| Variant | Description | Usage |
|---------|-------------|-------|
| default | Primary colored button | Main actions |
| outline | Bordered button | Secondary actions |
| secondary | Alternative color button | Alternative actions |
| ghost | Text-only button | Subtle actions |
| link | Appears as a link | Navigation actions |
| destructive | Red button | Dangerous actions |

Button sizes:

| Size | Description | Height | Usage |
|------|-------------|--------|-------|
| default | Standard size | auto | Reserved for special cases |
| sm | Small size | h-8 (2rem) | Standard button size for most UI elements |
| lg | Large size | auto | Reserved for special emphasis |
| icon | Square icon button | h-8 w-8 (2rem) | Icon-only buttons |

#### Button Usage Guidelines

- Use `size="sm"` with `className="h-8"` for most buttons to maintain consistent height
- Use `size="icon"` with `className="h-8 w-8"` for icon buttons
- For buttons with icons and text, use `className="gap-2"` with icon size of `h-3 w-3`
- Use `text-xs` for button text in compact UIs

### Cards

Cards are used to group related content:

| Component | Purpose |
|-----------|---------|
| Card | Container component |
| CardHeader | Card title section with `className="pb-2"` for compact spacing |
| CardTitle | Card heading with `className="text-sm font-medium"` |
| CardDescription | Secondary text with `className="text-xs"` |
| CardContent | Main content area |
| CardFooter | Actions area |

#### Card Usage Guidelines

- Use `<CardHeader className="pb-2">` for compact card headers
- Use `<CardTitle className="text-sm font-medium">` for card titles
- Use `<CardDescription className="text-xs">` for card descriptions
- For card headers with multiple elements, wrap in a flex container:
  ```jsx
  <CardHeader className="pb-2">
    <div className="flex flex-row items-center justify-between">
      <CardTitle className="text-sm font-medium">Title</CardTitle>
      <Button variant="link" className="h-auto p-0 text-xs">Action</Button>
    </div>
  </CardHeader>
  ```

### Form Elements

| Component | Purpose | Styling |
|-----------|---------|---------|
| Input | Text input field | `className="h-9"` for compact height |
| Textarea | Multi-line text input | Standard sizing |
| Select | Dropdown selection | `className="h-9"` for compact height |
| Checkbox | Toggle selection | Standard sizing |
| RadioGroup | Single selection from options | Standard sizing |
| Switch | Toggle switch | Standard sizing |
| Slider | Range input | Standard sizing |
| Label | Form field label | `className="text-xs"` for compact labels |

#### Form Element Usage Guidelines

- Use `<Input className="h-9" />` for compact input fields
- Use `<Label className="text-xs">` for form labels
- For search inputs with icons:
  ```jsx
  <div className="relative">
    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
    <Input placeholder="Search..." className="pl-8 h-9" />
  </div>
  ```

## Animations

The design system includes predefined animations for common UI patterns:

| Animation | Description | Usage |
|-----------|-------------|-------|
| accordion-down | Expand downward | Accordions, dropdowns |
| accordion-up | Collapse upward | Accordions, dropdowns |
| fade-in | Fade in from transparent | Modals, alerts |
| fade-out | Fade out to transparent | Dismissing elements |
| pulse | Pulsing highlight | Tour highlights, alerts |

```css
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}
```

## Dark Mode Implementation

### CSS Variables Approach

Dark mode is implemented using CSS variables in combination with a class-based approach. The `:root` selector defines variables for light mode, while the `.dark` class overrides these variables for dark mode:

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* ... other variables ... */
}

.dark {
  --background: 222 18% 11%;
  --foreground: 210 40% 98%;
  /* ... other dark mode variables ... */
}
```

### Component Adaptations

Components automatically adapt to light or dark mode by referencing these CSS variables:

```css
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

### Dark Mode-Specific Styles

Some elements have dark mode-specific overrides:

```css
.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}
```

## Accessibility Considerations

### Color Contrast

All color combinations meet WCAG 2.1 AA standards for contrast:

- Text on backgrounds maintains at least 4.5:1 contrast ratio
- Large text maintains at least 3:1 contrast ratio
- UI components maintain at least 3:1 contrast against adjacent colors

### Focus Indicators

All interactive elements have visible focus states using the `--ring` color variable:

```css
:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}
```

### Screen Reader Support

- All interactive elements have appropriate ARIA attributes
- Icon-only buttons include `<span className="sr-only">Button description</span>`
- Complex components use proper ARIA roles and states

## Responsive Design

### Breakpoints

The application uses the following breakpoints:

| Breakpoint | Width | Description |
|------------|-------|-------------|
| xs | 390px | Small mobile devices |
| sm | 640px | Mobile devices |
| md | 768px | Tablets |
| lg | 1024px | Laptops |
| xl | 1280px | Desktops |
| 2xl | 1536px | Large desktops |

### Container Widths

Container elements adapt to screen size:

```js
container: {
  center: true,
  padding: {
    DEFAULT: "1rem",
    sm: "1.5rem",
    md: "2rem",
  },
  screens: {
    "2xl": "1400px",
  },
}
```

## Best Practices

1. **Color Usage**
   - Use semantic colors for their intended purpose
   - Maintain sufficient contrast in both light and dark modes
   - Don't rely on color alone to convey information
   - Use blue colors specifically for AI-powered features to distinguish them

2. **Typography**
   - Use `text-xs` for secondary information, metadata, and compact UI elements
   - Use `text-sm` for card titles, form labels, and most UI components
   - Use `text-base` for regular body text and page subtitles
   - Use `text-2xl` for page titles
   - Avoid using larger font sizes (text-3xl and above) except for hero sections
   - Ensure text remains readable at all screen sizes
   - Use consistent typography in document engine for professional legal documents

3. **Components**
   - Use the established component library instead of creating custom elements
   - Use `size="sm"` with `className="h-8"` for most buttons
   - Use `className="pb-2"` for card headers to maintain compact spacing
   - Use `className="text-sm font-medium"` for card titles
   - Use `className="text-xs"` for card descriptions and secondary text
   - Use `className="p-4"` for page containers
   - Use `className="mb-4"` for section spacing
   - Use `className="gap-3"` or `className="gap-4"` for grid and flex layouts
   - Maintain consistent spacing and alignment across all pages
   - Use document engine components for professional document editing

4. **Layout & Spacing**
   - Use `p-4` for page padding
   - Use `mb-4` for vertical spacing between sections
   - Use `gap-3` for spacing between related elements
   - Use `gap-4` for spacing between distinct sections
   - Use compact spacing for related elements (gap-2)
   - Maintain consistent spacing patterns across all pages
   - Implement responsive design for mobile-first approach

5. **Theming**
   - Test all components in both light and dark modes
   - Use the CSS variables for colors instead of hardcoded values
   - Test system theme changes for proper transitions
   - Ensure landing page maintains consistent theming with main application

6. **Performance**
   - Use optimistic UI updates for workspace switching
   - Implement debounced updates in document engine
   - Use memoization for expensive computations
   - Implement proper loading states and skeleton screens

7. **Accessibility**
   - Ensure all interactive elements are keyboard accessible
   - Provide text alternatives for non-text content
   - Test with screen readers and other assistive technologies
   - Maintain sufficient contrast ratios for text and UI elements
   - Implement proper ARIA labels and roles for complex components