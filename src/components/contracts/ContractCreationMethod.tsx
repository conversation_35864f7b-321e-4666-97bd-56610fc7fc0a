import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import UnifiedImportModal from "../modals/UnifiedImportModal";
import { ArrowLeft, FileText, Wand2, Sparkles, Import, Copy, Users, Check, BookTemplate, Lightbulb, Loader2, Send } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";

const ContractCreationMethod = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("template");
  const [aiPrompt, setAiPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
    const [showImportModal, setShowImportModal] = useState(false);

  // Check if we're on the import route and show the import modal
  useEffect(() => {
    if (location.pathname === "/contracts/import") {
      setShowImportModal(true);
    }
  }, [location.pathname]);

  // Handle import modal close
  const handleImportModalChange = (open: boolean) => {
    setShowImportModal(open);
    // If we're on the import route and the modal is closed, navigate back to create
    if (!open && location.pathname === "/app/contracts/import") {
      navigate("/app/contracts/create");
    }
  };

  const handleUseTemplate = () => {
    navigate("/app/contracts/templates");
  };

  const handleStartFromScratch = () => {
    navigate("/app/contracts/wizard");
  };

  const handleAIGenerate = () => {
    if (!aiPrompt.trim()) return;

    setIsGenerating(true);
    // Simulate AI processing
    setTimeout(() => {
      setIsGenerating(false);
      // For now, just show a toast that it's coming soon
      // In the future, this would generate the contract directly
      console.log("AI Generation requested:", aiPrompt);
    }, 2000);
  };



  return (
    <div className="w-full h-full bg-background p-4 overflow-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold">Create Contract</h1>
          <p className="text-base text-muted-foreground mt-1">
            Choose how you&apos;d like to get started
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate("/contracts")}
          className="flex items-center h-9"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>




      {/* Main Content - Focused Three-Tab Interface */}
      <div className="max-w-4xl mx-auto">
        <Tabs
          defaultValue="template"
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-3 mb-8 h-12">
            <TabsTrigger value="template" className="flex items-center gap-2 text-sm h-10">
              <FileText className="h-5 w-5" />
              <span>Templates</span>
            </TabsTrigger>
            <TabsTrigger value="scratch" className="flex items-center gap-2 text-sm h-10">
              <Wand2 className="h-5 w-5" />
              <span>Custom</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2 text-sm h-10">
              <Sparkles className="h-5 w-5" />
              <span>AI Generate</span>
            </TabsTrigger>
          </TabsList>

              <TabsContent value="template" className="space-y-6">
                <Card>
                  <CardHeader className="pb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg font-medium">Use Template</CardTitle>
                    <CardDescription className="text-sm">
                      Start with professionally drafted templates
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <div className="grid grid-cols-2 gap-3 mb-6">
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-green-100 flex items-center justify-center">
                              <span className="text-xs">🔒</span>
                            </div>
                            <div className="font-medium text-sm">NDA</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Confidentiality protection
                          </div>
                        </Card>
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-blue-100 flex items-center justify-center">
                              <span className="text-xs">🛠️</span>
                            </div>
                            <div className="font-medium text-sm">Services</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Service agreements
                          </div>
                        </Card>
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-purple-100 flex items-center justify-center">
                              <span className="text-xs">👤</span>
                            </div>
                            <div className="font-medium text-sm">Employment</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Hiring contracts
                          </div>
                        </Card>
                        <Card className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-5 h-5 rounded bg-amber-100 flex items-center justify-center">
                              <span className="text-xs">⚖️</span>
                            </div>
                            <div className="font-medium text-sm">License</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Software licensing
                          </div>
                        </Card>
                      </div>

                      <div className="flex items-center gap-3 text-xs text-muted-foreground mb-4">
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Legally vetted</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <BookTemplate className="h-3 w-3 text-blue-600" />
                          <span>Fully customizable</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Lightbulb className="h-3 w-3 text-amber-600" />
                          <span>Smart suggestions</span>
                        </div>
                      </div>
                    </div>
                    <Button className="w-full h-10 text-sm" onClick={handleUseTemplate}>
                      Browse All Templates
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="scratch" className="space-y-6">
                <Card>
                  <CardHeader className="pb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <Wand2 className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg font-medium">Start from Scratch</CardTitle>
                    <CardDescription className="text-sm">
                      Build custom contracts with guided steps
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <div className="bg-secondary/20 p-4 rounded-lg mb-6">
                        <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                          <span className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary">7</span>
                          Step-by-step wizard
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">1</span>
                            </div>
                            <span>Jurisdiction & type</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">2</span>
                            </div>
                            <span>Parties information</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">3</span>
                            </div>
                            <span>Contract terms</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">4</span>
                            </div>
                            <span>Legal clauses</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">5</span>
                            </div>
                            <span>Industry provisions</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">6</span>
                            </div>
                            <span>Attachments</span>
                          </div>
                          <div className="flex items-center gap-2 md:col-span-2">
                            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium text-primary">7</span>
                            </div>
                            <span>Review & approval</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Full control</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Auto-save</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Check className="h-3 w-3 text-green-600" />
                          <span>Unique agreements</span>
                        </div>
                      </div>
                    </div>
                    <Button className="w-full h-10 text-sm" onClick={handleStartFromScratch}>
                      Start Building Contract
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="ai" className="space-y-6">
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
                      <Sparkles className="h-4 w-4 text-purple-600" />
                    </div>
                    <h2 className="text-lg font-medium">AI-Assisted</h2>
                    <Badge className="bg-amber-100 text-amber-800 border-amber-300 text-xs py-0.5 px-2">Coming Soon</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Generate contracts from simple descriptions
                  </p>
                </div>

                {/* AI Prompt Interface - Minimal ChatGPT Style */}
                <div className="flex flex-col items-center justify-center min-h-[500px]">
                  {/* Main Prompt Input */}
                  <div className="w-full max-w-4xl">
                    <div className="relative">
                      <Textarea
                        placeholder="Type your idea and we'll build it together."
                        className="w-full min-h-[180px] text-sm p-8 pr-20 rounded-2xl border-2 border-muted-foreground/20 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 resize-none bg-background/50 backdrop-blur-sm"
                        value={aiPrompt}
                        onChange={(e) => setAiPrompt(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                            handleAIGenerate();
                          }
                        }}
                      />

                      {/* Action Buttons */}
                      <div className="absolute bottom-4 right-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 rounded-full hover:bg-muted"
                          onClick={() => {/* Attach files functionality */}}
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                          </svg>
                        </Button>

                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 rounded-full hover:bg-muted"
                          onClick={() => {/* Star/favorite functionality */}}
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                          </svg>
                        </Button>

                        {/* Send Button */}
                        <Button
                          size="sm"
                          onClick={handleAIGenerate}
                          disabled={!aiPrompt.trim() || isGenerating}
                          className="h-8 w-8 p-0 rounded-full bg-purple-500 hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isGenerating ? (
                            <Loader2 className="h-4 w-4 animate-spin text-white" />
                          ) : (
                            <Send className="h-4 w-4 text-white" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Coming Soon Notice */}
                  <div className="text-center mt-8">
                    <p className="text-xs text-muted-foreground">
                      ✨ Advanced AI contract generation coming soon
                    </p>
                  </div>
                </div>
              </TabsContent>
        </Tabs>

        {/* Secondary Options - Below main flow */}
        <div className="mt-12 pt-8 border-t border-border">
          <div className="text-center mb-6">
            <h4 className="text-lg font-medium mb-2">Other Ways to Create</h4>
            <p className="text-sm text-muted-foreground">Alternative methods for specific needs</p>
          </div>

          <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
            <Button
              variant="outline"
              className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
              onClick={() => handleImportModalChange(true)}
            >
              <Import className="h-6 w-6 text-muted-foreground" />
              <div className="text-center">
                <div className="font-medium text-sm">Import Document</div>
                <div className="text-xs text-muted-foreground">Upload existing files</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
              onClick={() => navigate("/clone")}
            >
              <Copy className="h-6 w-6 text-muted-foreground" />
              <div className="text-center">
                <div className="font-medium text-sm">Clone Contract</div>
                <div className="text-xs text-muted-foreground">Copy existing contract</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
              onClick={() => navigate("/request")}
            >
              <Users className="h-6 w-6 text-muted-foreground" />
              <div className="text-center">
                <div className="font-medium text-sm">Request Help</div>
                <div className="text-xs text-muted-foreground">Get legal team assistance</div>
              </div>
            </Button>
          </div>
        </div>
      </div>

      {/* Import Document Modal */}
      <UnifiedImportModal
        open={showImportModal}
        onOpenChange={handleImportModalChange}
        redirectToWizard={true}
      />


    </div>
  );
};



export default ContractCreationMethod;