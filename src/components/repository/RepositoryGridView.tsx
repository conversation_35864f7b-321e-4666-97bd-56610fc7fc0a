import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ead<PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

import { Download, FileText, MoreHorizontal, Star, Copy, ArrowRight, Loader2, Eye } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useWorkspace } from "@/lib/workspace-provider";
import { Template, templateStore } from "@/types/template";
import { useDocumentPreview } from "@/engines/document-engine/hooks/useDocumentPreview";
import { DocumentPreviewModal } from "@/engines/document-engine/preview/DocumentPreviewModal";
import { useApi } from "@/lib/api";
import { ContractService, TemplateService, DocumentService } from "@/services/api-services";
import UnifiedDocumentViewerModal from "../modals/UnifiedDocumentViewerModal";
import type { Contract as ApiContract, Template as ApiTemplate } from "@/services/api-types";

interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "active" | "expired" | "terminated" | "pending_approval" | "rejected";
  createdBy: {
    name: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  tags: string[];
  starred: boolean;
  workspaceId?: string; // Add workspace ID to associate contracts with workspaces
  folderId?: string; // Add folder ID to associate contracts with folders
}

interface RepositoryGridViewProps {
  searchQuery: string;
  selectedFolderId: string | null;
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  showStarredOnly: boolean;
  selectedUsers: string[];
}

const RepositoryGridView = ({
  searchQuery,
  selectedFolderId,
  dateRange,
  showStarredOnly,
  selectedUsers,
}: RepositoryGridViewProps) => {
  // Get current workspace from context
  const { currentWorkspace, canAccessContent } = useClerkWorkspace();
  const { fetch, fetchArray } = useApi();

  // State for data
  const [templates, setTemplates] = useState<Template[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for document viewer modal
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);

  // Document preview hook
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  // Load templates from local storage and API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Load templates from local storage first
        templateStore.loadFromLocalStorage();
        setTemplates(templateStore.getTemplates());

        // Then fetch templates from API
        if (currentWorkspace) {
          try {
            // Fetch templates from API using fetchArray to ensure we get an array
            const templatesResult = await fetchArray(
              () => TemplateService.getTemplates({ workspace_id: currentWorkspace.id }),
              "Loading templates...",
              "Failed to load templates"
            );

          // Debug the templates result
          console.log("Templates result:", templatesResult);
          console.log("Templates result type:", typeof templatesResult);
          console.log("Is array:", Array.isArray(templatesResult));

          if (templatesResult && templatesResult.length > 0) {
            // Map API templates to UI templates
            const apiTemplates = templatesResult.map((template: ApiTemplate) => ({
              id: template.id,
              title: template.title,
              description: template.description,
              type: template.type,
              complexity: template.complexity,
              industry: template.industry || '',
              tags: template.tags || [],
              icon: template.icon || '',
              lastUpdated: template.updated_at || template.created_at,
              usageCount: template.usage_count,
              isUserCreated: template.is_user_created,
              folderId: template.folder_id || 'folder-4', // Default to templates folder
            }));

            // Merge with local templates
            const localTemplates = templateStore.getTemplates();
            const mergedTemplates = [...localTemplates];

            // Add API templates that don't exist locally
            apiTemplates.forEach(apiTemplate => {
              if (!localTemplates.some(localTemplate => localTemplate.id === apiTemplate.id)) {
                mergedTemplates.push(apiTemplate);
              }
            });

            setTemplates(mergedTemplates);

            // Update local storage with merged templates
            templateStore.setTemplates(mergedTemplates);
            templateStore.saveToLocalStorage();
            }
          } catch (templateError) {
            console.error("Error fetching templates:", templateError);
            // Continue with contracts even if templates fail
          }
        }

        if (currentWorkspace) {
          try {
            // Fetch contracts from API using fetchArray to ensure we get an array
            const contractsResult = await fetchArray(
              () => ContractService.getContracts({ workspace_id: currentWorkspace.id }),
              "Loading contracts...",
              "Failed to load contracts"
            );

          // Debug the contracts result
          console.log("Contracts result:", contractsResult);
          console.log("Contracts result type:", typeof contractsResult);
          console.log("Is array (contracts):", Array.isArray(contractsResult));

          if (contractsResult && contractsResult.length > 0) {
            // Map API contracts to UI contracts
            const mappedContracts: Contract[] = contractsResult.map((contract: ApiContract) => ({
              id: contract.id,
              title: contract.title,
              type: contract.type,
              status: contract.status,
              createdBy: {
                name: contract.created_by?.name || 'Unknown',
                id: contract.created_by?.id || 'unknown-id'
              },
              createdDate: contract.created_at,
              expiryDate: contract.expiry_date,
              counterparty: contract.counterparty || 'N/A',
              tags: contract.tags || [],
              starred: contract.starred || false,
              workspaceId: contract.workspace_id,
              folderId: contract.folder_id || 'folder-1' // Default to Contracts folder
            }));

            setContracts(mappedContracts);
            }
          } catch (contractError) {
            console.error("Error fetching contracts:", contractError);
            // Continue with local data even if contracts API fails
          }
        }
      } catch (err) {
        console.error("Error fetching repository data:", err);
        setError("Failed to load repository data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentWorkspace?.id]); // Only depend on workspace ID to prevent infinite loops

  // Handle template selection
  const handleUseTemplate = (templateId: string) => {
    // Increment usage count
    templateStore.incrementUsageCount(templateId);
    // Navigate to contract wizard with template
    window.location.href = `/app/contracts/wizard?template=${templateId}`;
  };

  // Handle document view
  const handleViewDocument = (documentId: string) => {
    setSelectedDocumentId(documentId);
    setIsViewerOpen(true);
  };

  // Filter contracts based on filters
  const filteredContracts = contracts.filter((contract) => {
    // If templates folder is selected, don't show contracts
    if (selectedFolderId === "folder-4") {
      return false;
    }

    // Filter by workspace - only show contracts from the current workspace
    // AND ensure user has access to the workspace the contract belongs to
    const matchesWorkspace = currentWorkspace ?
      contract.workspaceId === currentWorkspace.id && canAccessContent(contract.workspaceId) :
      false;

    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      contract.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.counterparty.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.type.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by folder
    const matchesFolder =
      selectedFolderId === null ||
      contract.folderId === selectedFolderId;

    // Filter by date range
    const contractDate = new Date(contract.createdDate);
    const matchesDateRange =
      !dateRange.from ||
      ((!dateRange.from || contractDate >= dateRange.from) &&
       (!dateRange.to || contractDate <= dateRange.to));

    // Filter by starred status
    const matchesStarred =
      !showStarredOnly ||
      contract.starred;

    // Filter by creator
    const matchesCreator =
      selectedUsers.length === 0 ||
      (contract.createdBy.id && selectedUsers.includes(contract.createdBy.id));

    return matchesWorkspace && matchesSearch && matchesFolder && matchesDateRange && matchesStarred && matchesCreator;
  });

  // Filter templates based on filters
  const filteredTemplates = templates.filter((template) => {
    // Only show templates when templates folder is selected or no folder is selected
    const matchesFolder =
      selectedFolderId === "folder-4" ||
      (selectedFolderId === null && template.folderId === "folder-4");

    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.tags && template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));

    return matchesFolder && matchesSearch;
  });

  const getStatusBadge = (status: Contract["status"]) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "pending_approval":
        return <Badge variant="secondary">In Review</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "expired":
        return <Badge variant="destructive">Expired</Badge>;
      case "terminated":
        return <Badge variant="destructive">Terminated</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to simplify contract titles
  const simplifyTitle = (title: string) => {
    // Remove "with Company Name" and other common phrases
    let simplified = title
      .replace(/\s+with\s+[^,]+/i, '')
      .replace(/\s+-\s+[^,]+/i, '')
      .trim();

    // If still too long, truncate
    if (simplified.length > 20) {
      simplified = simplified.substring(0, 20).trim();
    }

    return simplified;
  };

  // Get template complexity badge
  const getComplexityBadge = (complexity: Template["complexity"]) => {
    switch (complexity) {
      case "simple":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Simple</Badge>;
      case "medium":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Medium</Badge>;
      case "complex":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Complex</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading repository data...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch your documents
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading repository</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : filteredContracts.length === 0 && filteredTemplates.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No documents found</h3>
          <p className="text-muted-foreground mt-2">
            {searchQuery || selectedFolderId || dateRange.from || showStarredOnly || selectedUsers.length > 0
              ? "Try adjusting your filters"
              : "Add documents to your repository to get started"}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Display templates if in templates folder */}
          {filteredTemplates.map((template) => (
            <Card
              key={template.id}
              className="overflow-hidden hover:shadow-md transition-shadow"
            >
              <div className="relative p-3 bg-muted">
                <div className="flex items-center mb-2">
                  <FileText className="h-6 w-6 text-primary mr-2" />
                  <div className="text-sm font-medium text-foreground">
                    {template.title}
                  </div>
                </div>
              </div>
              <CardContent className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">{template.type}</Badge>
                  {getComplexityBadge(template.complexity)}
                </div>
                <div className="text-xs text-muted-foreground mb-1 line-clamp-2">
                  {template.description}
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <span className="mr-2">
                    Updated: {formatDate(template.lastUpdated)}
                  </span>
                  <span>Used: {template.usageCount} times</span>
                </div>
              </CardContent>
              <CardFooter className="p-3 pt-0 flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-xs text-muted-foreground">
                    {template.industry || "General"}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 text-xs"
                    onClick={() => handleUseTemplate(template.id)}
                  >
                    Use Template
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-7 w-7">
                        <MoreHorizontal className="h-3.5 w-3.5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => openPreview(`<h1>${template.title}</h1><p>${template.description}</p><p><strong>Type:</strong> ${template.type}</p><p><strong>Complexity:</strong> ${template.complexity}</p>`, `${template.title} - Preview`)}>
                        <Eye className="h-3.5 w-3.5 mr-2" />
                        Preview Template
                      </DropdownMenuItem>
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      <DropdownMenuItem>
                        <Copy className="h-3.5 w-3.5 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive">
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardFooter>
            </Card>
          ))}

          {/* Display contracts */}
          {filteredContracts.map((contract) => (
            <Card
              key={contract.id}
              className="overflow-hidden hover:shadow-md transition-shadow"
              onClick={() => window.location.href = `/app/contracts/${contract.id}`}
            >
              <div className="relative p-3 bg-muted">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-1 right-1 h-6 w-6 bg-background/80 hover:bg-background/90 rounded-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Toggle star status (would be implemented with API call)
                  }}
                >
                  <Star
                    className={`h-3 w-3 ${contract.starred ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`}
                  />
                </Button>
                <div className="flex items-center mb-2">
                  <FileText className="h-6 w-6 text-primary mr-2" />
                  <div className="text-sm font-medium text-foreground">
                    {simplifyTitle(contract.title)}
                  </div>
                </div>
              </div>
              <CardContent className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">{contract.type}</Badge>
                  {getStatusBadge(contract.status)}
                </div>
                <div className="text-xs text-muted-foreground mb-1">
                  {contract.counterparty}
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <span className="mr-2">
                    {formatDate(contract.createdDate)}
                  </span>
                  {contract.expiryDate && (
                    <span>→ {formatDate(contract.expiryDate)}</span>
                  )}
                </div>
              </CardContent>
              <CardFooter className="p-3 pt-0 flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-xs text-muted-foreground">
                    By: {contract.createdBy.name}
                  </span>
                </div>
                <div className="flex items-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewDocument(contract.id);
                    }}
                  >
                    <Eye className="h-3.5 w-3.5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Download contract (would be implemented with API call)
                    }}
                  >
                    <Download className="h-3.5 w-3.5" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-3.5 w-3.5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                      <DropdownMenuItem onClick={() => openPreview(`<h1>${contract.title}</h1><p><strong>Type:</strong> ${contract.type}</p><p><strong>Status:</strong> ${contract.status}</p><p><strong>Counterparty:</strong> ${contract.counterparty}</p><p><strong>Created:</strong> ${contract.createdDate}</p>${contract.expiryDate ? `<p><strong>Expires:</strong> ${contract.expiryDate}</p>` : ''}`, `${contract.title} - Preview`)}>
                        <Eye className="h-3.5 w-3.5 mr-2" />
                        Quick Preview
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => window.location.href = `/contracts/${contract.id}`}>
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleViewDocument(contract.id)}>
                        <Eye className="h-3.5 w-3.5 mr-2" />
                        View Document
                      </DropdownMenuItem>
                      <DropdownMenuItem>Download</DropdownMenuItem>
                      <DropdownMenuItem>Share</DropdownMenuItem>
                      <DropdownMenuItem>Rename</DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive">
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Document Viewer Modal */}
      {selectedDocumentId && (
        <UnifiedDocumentViewerModal
          open={isViewerOpen}
          onOpenChange={setIsViewerOpen}
          documentId={selectedDocumentId}
          readOnly={true}
          showActions={true}
        />
      )}

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true
        }}
      />
    </div>
  );
};

export default RepositoryGridView;
